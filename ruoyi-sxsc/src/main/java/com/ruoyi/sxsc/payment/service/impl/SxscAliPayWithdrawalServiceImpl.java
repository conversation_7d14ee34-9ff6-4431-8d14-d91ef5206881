package com.ruoyi.sxsc.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;
import com.ruoyi.sxsc.payment.mapper.SxscAliPayWithdrawalMapper;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.utils.AliPayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 支付宝提现信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-29
 */
@Service
public class SxscAliPayWithdrawalServiceImpl extends ServiceImpl<SxscAliPayWithdrawalMapper,SxscAliPayWithdrawal> implements ISxscAliPayWithdrawalService
{

    @Autowired
    AliPayUtils aliPayUtils;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    SxscAliPayWithdrawalMapper aliPayWithdrawalMapper;

    @Autowired
    ISxscUserCommissionOrderService iSxscUserCommissionOrderService;



    /**
     * 查询支付宝提现信息
     * 
     * @param id 支付宝提现信息主键
     * @return 支付宝提现信息
     */
    @Override
    public SxscAliPayWithdrawal selectSxscAliPayWithdrawalById(String id)
    {
        return getById(id);
    }

    /**
     * 查询支付宝提现总金额
     *
     * @param orderName 订单名称
     * @param month 月份
     * @return 支付宝提现信息
     */
    @Override
    public BigDecimal aliPayWithdrawalSum(String orderName,String month){
        return aliPayWithdrawalMapper.aliPayWithdrawalSum(orderName,month);
    }

    /**
     * 查询支付宝提现信息列表
     * 
     * @param sxscAliPayWithdrawal 支付宝提现信息
     * @return 支付宝提现信息
     */
    @Override
    public List<SxscAliPayWithdrawal> selectSxscAliPayWithdrawalList(SxscAliPayWithdrawal sxscAliPayWithdrawal)
    {
        LambdaQueryWrapper<SxscAliPayWithdrawal> wrapper=new LambdaQueryWrapper();

        wrapper.like(StringUtils.isNotNull(sxscAliPayWithdrawal.getOrderId()),SxscAliPayWithdrawal::getOrderId,sxscAliPayWithdrawal.getOrderId());

        wrapper.like(StringUtils.isNotNull(sxscAliPayWithdrawal.getStatus()),SxscAliPayWithdrawal::getStatus,sxscAliPayWithdrawal.getStatus());

        wrapper.like(StringUtils.isNotNull(sxscAliPayWithdrawal.getPayeeAccount()),SxscAliPayWithdrawal::getPayeeAccount,sxscAliPayWithdrawal.getPayeeAccount());

        wrapper.like(StringUtils.isNotNull(sxscAliPayWithdrawal.getPayeeRealName()),SxscAliPayWithdrawal::getPayeeRealName,sxscAliPayWithdrawal.getPayeeRealName());

        wrapper.like(StringUtils.isNotNull(sxscAliPayWithdrawal.getOrderName()),SxscAliPayWithdrawal::getOrderName,sxscAliPayWithdrawal.getOrderName());

        wrapper.eq(StringUtils.isNotNull(sxscAliPayWithdrawal.getUserId()),SxscAliPayWithdrawal::getUserId,sxscAliPayWithdrawal.getUserId());

        if(StringUtils.isNotNull(sxscAliPayWithdrawal.getCreateTime())){
            wrapper.apply("DATE_FORMAT(create_time,'%Y-%m-%d') = '"+ DateUtils.dateTime(sxscAliPayWithdrawal.getCreateTime())+"'");
        }

        wrapper.orderByDesc(SxscAliPayWithdrawal::getCreateTime);

        return list(wrapper);
    }

    /**
     * 新增支付宝提现信息
     * 
     * @param amount 金额
     * @param userId 用户主键
     * @return 结果
     */
    @Override
    public SxscAliPayWithdrawal insertSxscAliPayWithdrawal(BigDecimal amount,Long userId,String orderId,String orderName)
    {

        SxscAliPayWithdrawal withdrawal=getById(orderId);
        if(StringUtils.isNotNull(withdrawal)){
            return withdrawal;
        }
        SxscAliPayWithdrawal sxscAliPayWithdrawal =new SxscAliPayWithdrawal();
        SxscUserInfo userInfo=iSxscUserInfoService.getById(userId);
        sxscAliPayWithdrawal.setOrderId(orderId);
        sxscAliPayWithdrawal.setId(orderId);
        sxscAliPayWithdrawal.setAmount(amount);
        sxscAliPayWithdrawal.setOrderName(orderName);
        sxscAliPayWithdrawal.setUserId(userId);
        sxscAliPayWithdrawal.setStatus(0l);
        if(StringUtils.isNull(userInfo)){
            sxscAliPayWithdrawal.setCreateTime(DateUtils.getNowDate());
            save(sxscAliPayWithdrawal);
            return sxscAliPayWithdrawal;
        }
        sxscAliPayWithdrawal.setPayeeAccount(userInfo.getAliPayAcc());
        sxscAliPayWithdrawal.setPayeeRealName(userInfo.getAliPayName());
        sxscAliPayWithdrawal.setCreateTime(DateUtils.getNowDate());
        save(sxscAliPayWithdrawal);
        try {
            aliPayUtils.withdraw(sxscAliPayWithdrawal);
            if(sxscAliPayWithdrawal.getAlipayFundTransUniTransferResponse().getCode().equals("10000")){
                sxscAliPayWithdrawal.setStatus(1l);
            }else{
                sxscAliPayWithdrawal.setStatus(0l);
            }
        }catch (Exception e){
            sxscAliPayWithdrawal.setStatus(0l);
            e.printStackTrace();
        }
        updateById(sxscAliPayWithdrawal);
        return sxscAliPayWithdrawal;
    }


    /**
     * 重新发起提现
     *
     * @param id 订单退款
     * @return 结果
     */
    @Override
    public AjaxResult reissueRefund(String id)
    {
        SxscAliPayWithdrawal withdrawal=getById(id);
        if(StringUtils.isNull(withdrawal)||withdrawal.getStatus()!=0){
            return AjaxResult.error("无法发起提现");
        }
        try {
            SxscUserInfo userInfo=iSxscUserInfoService.getById(withdrawal.getUserId());
            withdrawal.setPayeeAccount(userInfo.getAliPayAcc());
            withdrawal.setPayeeRealName(userInfo.getAliPayName());
            aliPayUtils.withdraw(withdrawal);
            if(withdrawal.getAlipayFundTransUniTransferResponse().getCode().equals("10000")){
                withdrawal.setStatus(1l);
                iSxscUserCommissionOrderService.updateSxscUserCommissionOrder(id);
            }else{
                withdrawal.setStatus(0l);
            }
        }catch (Exception e){
            withdrawal.setStatus(0l);
            e.printStackTrace();
        }
        withdrawal.setUpdateBy(SecurityUtils.getUsername());
        withdrawal.setUpdateTime(DateUtils.getNowDate());
        updateById(withdrawal);
        return AjaxResult.success();
    }


}
