package com.ruoyi.sxsc.payment.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;
import com.ruoyi.sxsc.payment.mapper.SxscWeChatTransferMapper;
import com.ruoyi.sxsc.payment.service.ISxscWeChatTransferService;
import com.ruoyi.sxsc.utils.WeChatTransferUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 微信转账信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@Slf4j
@Service
public class SxscWeChatTransferServiceImpl extends ServiceImpl<SxscWeChatTransferMapper, SxscWeChatTransfer> implements ISxscWeChatTransferService
{

    @Autowired
    private WeChatTransferUtils weChatTransferUtils;

    @Autowired
    private SxscWeChatTransferMapper sxscWeChatTransferMapper;

    /**
     * 查询微信转账信息
     * 
     * @param id 微信转账信息主键
     * @return 微信转账信息
     */
    @Override
    public SxscWeChatTransfer selectSxscWeChatTransferById(String id)
    {
        return getById(id);
    }

    /**
     * 查询微信转账总金额
     * @param transferRemark 转账备注
     * @param month 月份
     * @return 微信转账总金额
     */
    @Override
    public BigDecimal weChatTransferSum(String transferRemark, String month){
        return sxscWeChatTransferMapper.weChatTransferSum(transferRemark, month);
    }

    /**
     * 查询微信转账信息列表
     * 
     * @param sxscWeChatTransfer 微信转账信息
     * @return 微信转账信息
     */
    @Override
    public List<SxscWeChatTransfer> selectSxscWeChatTransferList(SxscWeChatTransfer sxscWeChatTransfer)
    {
        LambdaQueryWrapper<SxscWeChatTransfer> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(sxscWeChatTransfer.getId())) {
            queryWrapper.eq(SxscWeChatTransfer::getId, sxscWeChatTransfer.getId());
        }
        if (StringUtils.isNotEmpty(sxscWeChatTransfer.getOrderId())) {
            queryWrapper.eq(SxscWeChatTransfer::getOrderId, sxscWeChatTransfer.getOrderId());
        }
        if (StringUtils.isNotEmpty(sxscWeChatTransfer.getOpenid())) {
            queryWrapper.eq(SxscWeChatTransfer::getOpenid, sxscWeChatTransfer.getOpenid());
        }
        if (StringUtils.isNotEmpty(sxscWeChatTransfer.getTransferStatus())) {
            queryWrapper.eq(SxscWeChatTransfer::getTransferStatus, sxscWeChatTransfer.getTransferStatus());
        }
        if (sxscWeChatTransfer.getStatus() != null) {
            queryWrapper.eq(SxscWeChatTransfer::getStatus, sxscWeChatTransfer.getStatus());
        }
        if (sxscWeChatTransfer.getUserId() != null) {
            queryWrapper.eq(SxscWeChatTransfer::getUserId, sxscWeChatTransfer.getUserId());
        }
        queryWrapper.orderByDesc(SxscWeChatTransfer::getCreateTime);
        return list(queryWrapper);
    }

    /**
     * 新增微信转账信息
     * 
     * @param amount 金额
     * @param userId 用户主键
     * @param orderId 订单主键
     * @param transferRemark 转账备注
     * @param openid 用户openid
     * @param userName 用户真实姓名
     * @return 结果
     */
    @Override
    public SxscWeChatTransfer insertSxscWeChatTransfer(BigDecimal amount, Long userId, String orderId, 
                                                      String transferRemark, String openid, String userName)
    {
        SxscWeChatTransfer sxscWeChatTransfer = new SxscWeChatTransfer();
        sxscWeChatTransfer.setId(IdUtils.fastSimpleUUID());
        sxscWeChatTransfer.setOrderId(orderId);
        sxscWeChatTransfer.setOutBatchNo("BATCH_" + System.currentTimeMillis());
        sxscWeChatTransfer.setOpenid(openid);
        sxscWeChatTransfer.setUserName(userName);
        sxscWeChatTransfer.setAmount(amount.multiply(new BigDecimal("100"))); // 转换为分
        sxscWeChatTransfer.setTransferRemark(transferRemark);
        sxscWeChatTransfer.setTransferScene("1000"); // 1000-商户活动
        sxscWeChatTransfer.setUserId(userId);
        sxscWeChatTransfer.setTransferType(1L); // 1-企业付款到零钱
        sxscWeChatTransfer.setCreateBy(SecurityUtils.getUsername());
        sxscWeChatTransfer.setCreateTime(DateUtils.getNowDate());
        
        // 发起转账
        boolean success = weChatTransferUtils.transferToBalance(sxscWeChatTransfer);
        if (success) {
            sxscWeChatTransfer.setTransferTime(new Date());
        }
        
        save(sxscWeChatTransfer);
        return sxscWeChatTransfer;
    }

    /**
     * 修改微信转账信息
     * 
     * @param sxscWeChatTransfer 微信转账信息
     * @return 结果
     */
    @Override
    public int updateSxscWeChatTransfer(SxscWeChatTransfer sxscWeChatTransfer)
    {
        sxscWeChatTransfer.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscWeChatTransfer) ? 1 : 0;
    }
    /**
     * 重新发起转账
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public AjaxResult reissueTransfer(String id)
    {
        SxscWeChatTransfer transfer = getById(id);
        if(StringUtils.isNull(transfer) || transfer.getStatus() != 0){
            return AjaxResult.error("无法发起转账");
        }
        
        try {
            // 重新生成批次号
            transfer.setOutBatchNo("BATCH_" + System.currentTimeMillis());
            boolean success = weChatTransferUtils.transferToBalance(transfer);
            if (success) {
                transfer.setTransferTime(new Date());
            }
            updateById(transfer);
            return AjaxResult.success("转账发起成功");
        }catch (Exception e){
            log.error("重新发起微信转账失败", e);
            return AjaxResult.error("转账发起失败: " + e.getMessage());
        }
    }

    /**
     * 更新转账状态
     * 
     * @param id 主键
     * @return 结果
     */
    @Override
    public AjaxResult updateTransferStatus(String id)
    {
        SxscWeChatTransfer transfer = getById(id);
        if(StringUtils.isNull(transfer)){
            return AjaxResult.error("转账记录不存在");
        }
        
        try {
            weChatTransferUtils.updateTransferStatus(transfer);
            updateById(transfer);
            return AjaxResult.success("状态更新成功", transfer);
        }catch (Exception e){
            log.error("更新微信转账状态失败", e);
            return AjaxResult.error("状态更新失败: " + e.getMessage());
        }
    }
}
