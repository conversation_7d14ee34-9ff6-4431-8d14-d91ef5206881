package com.ruoyi.sxsc.payment.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityStaticCodeOrderService;
import com.ruoyi.sxsc.config.WeChatPayConfig;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumePurchaseService;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;
import com.ruoyi.sxsc.payment.mapper.SxscWeChatPayOrderMapper;
import com.ruoyi.sxsc.payment.service.ISxscWeChatPayOrderService;
import com.ruoyi.sxsc.utils.WeChatPayUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.notification.NotificationParser;
import com.wechat.pay.java.core.notification.RequestParam;
import com.wechat.pay.java.service.payments.model.Transaction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 微信支付订单信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@Slf4j
@Service
public class SxscWeChatPayOrderServiceImpl extends ServiceImpl<SxscWeChatPayOrderMapper, SxscWeChatPayOrder> implements ISxscWeChatPayOrderService
{

    @Autowired
    private WeChatPayUtils weChatPayUtils;

    @Autowired
    private WeChatPayConfig weChatPayConfig;

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscCommodityStaticCodeOrderService iSxscCommodityStaticCodeOrderService;

    @Autowired
    private SxscWeChatPayOrderMapper sxscWeChatPayOrderMapper;

    @Autowired
    private ISxscUserConsumePurchaseService iSxscUserConsumePurchaseService;

    @Autowired
    private ISxscCommodityService iSxscCommodityService;

    /**
     * 查询微信支付订单信息
     * 
     * @param id 微信支付订单信息主键
     * @return 微信支付订单信息
     */
    @Override
    public SxscWeChatPayOrder selectSxscWeChatPayOrderById(String id)
    {
        return getById(id);
    }

    /**
     * 查询微信支付订单支付总金额
     * @param month 月份
     * @param userId 用户主键
     * @return 微信支付订单信息
     */
    @Override
    public BigDecimal weChatPaySum(String month, Long userId){
        return sxscWeChatPayOrderMapper.weChatPaySum(month, userId);
    }

    /**
     * 查询微信支付订单信息列表
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 微信支付订单信息
     */
    @Override
    public List<SxscWeChatPayOrder> selectSxscWeChatPayOrderList(SxscWeChatPayOrder sxscWeChatPayOrder)
    {
        LambdaQueryWrapper<SxscWeChatPayOrder> queryWrapper = new LambdaQueryWrapper<>();
        if (StringUtils.isNotEmpty(sxscWeChatPayOrder.getId())) {
            queryWrapper.eq(SxscWeChatPayOrder::getId, sxscWeChatPayOrder.getId());
        }
        if (StringUtils.isNotEmpty(sxscWeChatPayOrder.getSubject())) {
            queryWrapper.like(SxscWeChatPayOrder::getSubject, sxscWeChatPayOrder.getSubject());
        }
        if (sxscWeChatPayOrder.getPayStatus() != null) {
            queryWrapper.eq(SxscWeChatPayOrder::getPayStatus, sxscWeChatPayOrder.getPayStatus());
        }
        if (sxscWeChatPayOrder.getBuyerUserId() != null) {
            queryWrapper.eq(SxscWeChatPayOrder::getBuyerUserId, sxscWeChatPayOrder.getBuyerUserId());
        }
        if (sxscWeChatPayOrder.getSellerUserId() != null) {
            queryWrapper.eq(SxscWeChatPayOrder::getSellerUserId, sxscWeChatPayOrder.getSellerUserId());
        }
        queryWrapper.orderByDesc(SxscWeChatPayOrder::getCreateTime);
        return list(queryWrapper);
    }

    /**
     * 新增微信支付订单信息
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 结果
     */
    @Override
    public String insertSxscWeChatPayOrder(SxscWeChatPayOrder sxscWeChatPayOrder)
    {
        SxscWeChatPayOrder weChatPayOrder = getById(sxscWeChatPayOrder.getId());
        if(StringUtils.isNotNull(weChatPayOrder)){
            return weChatPayOrder.getResponse();
        }
        sxscWeChatPayOrder.setCreateBy(SecurityUtils.getUsername());
        sxscWeChatPayOrder.setCreateTime(DateUtils.getNowDate());
        String res = weChatPayUtils.weChatPayOrder(sxscWeChatPayOrder);
        sxscWeChatPayOrder.setResponse(res);
        save(sxscWeChatPayOrder);
        return res;
    }

    /**
     * 修改微信支付订单信息
     * 
     * @param sxscWeChatPayOrder 微信支付订单信息
     * @return 结果
     */
    @Override
    public int updateSxscWeChatPayOrder(SxscWeChatPayOrder sxscWeChatPayOrder)
    {
        sxscWeChatPayOrder.setUpdateTime(DateUtils.getNowDate());
        return updateById(sxscWeChatPayOrder) ? 1 : 0;
    }

    /**
     * 批量删除微信支付订单信息
     * 
     * @param ids 需要删除的微信支付订单信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscWeChatPayOrderByIds(String[] ids)
    {
        return removeByIds(List.of(ids)) ? 1 : 0;
    }

    /**
     * 删除微信支付订单信息信息
     * 
     * @param id 微信支付订单信息主键
     * @return 结果
     */
    @Override
    public int deleteSxscWeChatPayOrderById(String id)
    {
        return removeById(id) ? 1 : 0;
    }

    /**
     * 微信支付成功回调处理
     */
    @Override
    public String notifyData(HttpServletRequest request, HttpServletResponse response) {
        try {
            // 读取请求体
            StringBuilder requestBody = new StringBuilder();
            try (BufferedReader reader = request.getReader()) {
                String line;
                while ((line = reader.readLine()) != null) {
                    requestBody.append(line);
                }
            }

            // 构建回调参数
            RequestParam requestParam = new RequestParam.Builder()
                    .serialNumber(request.getHeader("Wechatpay-Serial"))
                    .nonce(request.getHeader("Wechatpay-Nonce"))
                    .signature(request.getHeader("Wechatpay-Signature"))
                    .timestamp(request.getHeader("Wechatpay-Timestamp"))
                    .body(requestBody.toString())
                    .build();

            // 初始化配置
            Config config = new RSAAutoCertificateConfig.Builder()
                    .merchantId(weChatPayConfig.getMchId())
                    .privateKeyFromPath(weChatPayConfig.getPrivateKeyPath())
                    .merchantSerialNumber(weChatPayConfig.getMerchantSerialNumber())
                    .apiV3Key(weChatPayConfig.getApiV3Key())
                    .build();

            // 验证签名并解析回调数据
            NotificationParser parser = new NotificationParser(config);
            Transaction transaction = parser.parse(requestParam, Transaction.class);

            String outTradeNo = transaction.getOutTradeNo();
            String transactionId = transaction.getTransactionId();
            String tradeState = transaction.getTradeState().name();

            log.info("微信支付回调: outTradeNo={}, transactionId={}, tradeState={}", outTradeNo, transactionId, tradeState);

            // 查询订单
            SxscWeChatPayOrder payment = getById(outTradeNo);
            if (payment == null) {
                log.error("微信支付回调订单不存在: {}", outTradeNo);
                return "FAIL";
            }

            // 处理支付成功
            if ("SUCCESS".equals(tradeState) && payment.getPayStatus() == 0) {
                payment.setTransactionId(transactionId);
                payment.setActualTotalAmount(new BigDecimal(transaction.getAmount().getTotal()).divide(new BigDecimal("100")));
                payment.setNotifyDate(new Date());
                payment.setPayStatus(1L);
                payment.setNotifyResponse(requestBody.toString());
                updateById(payment);

                // 更新业务订单状态
                try {
                    if(payment.getOrderType() == 1){
                        iSxscCommodityOrderService.updateXgfmCommodityOrderPayment(outTradeNo, payment.getActualTotalAmount());
                    }else if(payment.getOrderType() == 2){
                        iSxscCommodityStaticCodeOrderService.updateSxscCommodityStaticCodeOrder(outTradeNo, payment.getSubject(), 1L, payment.getActualTotalAmount());
                    }else if(payment.getOrderType() == 3){
                        iSxscUserConsumePurchaseService.updateSxscUserConsumePurchase(outTradeNo, 1L);
                    }else if(payment.getOrderType() == 4){
                        iSxscCommodityService.updateSxscCommodityPayStatus(outTradeNo);
                    }
                }catch (Exception e){
                    log.error("更新业务订单状态失败", e);
                }
            }

            return "SUCCESS";
        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return "FAIL";
        }
    }

    /**
     * 查询订单状态
     */
    @Override
    public String queryOrderStatus(String outTradeNo) {
        JSONObject result = weChatPayUtils.queryOrder(outTradeNo);
        return result != null ? result.toJSONString() : null;
    }

    /**
     * 关闭订单
     */
    @Override
    public boolean closeOrder(String outTradeNo) {
        return weChatPayUtils.closeOrder(outTradeNo);
    }
}
