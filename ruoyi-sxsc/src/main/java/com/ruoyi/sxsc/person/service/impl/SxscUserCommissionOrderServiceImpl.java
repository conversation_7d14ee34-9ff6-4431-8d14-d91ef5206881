package com.ruoyi.sxsc.person.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.consume.service.ISxscUserConsumeQuotaDetailService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayWithdrawal;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;
import com.ruoyi.sxsc.payment.service.ISxscAliPayWithdrawalService;
import com.ruoyi.sxsc.payment.service.ISxscWeChatTransferService;
import com.ruoyi.sxsc.person.domain.SxscUserCommissionOrder;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.mapper.SxscUserCommissionMapper;
import com.ruoyi.sxsc.person.mapper.SxscUserCommissionOrderMapper;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionOrderService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionQuotaService;
import com.ruoyi.sxsc.person.service.ISxscUserCommissionService;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.sxsc.seting.service.ISxscSetingParameterService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

import static com.ruoyi.common.utils.DateUtils.YYYY_MM;

/**
 * 佣金提现Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-06-12
 */
@Service
public class SxscUserCommissionOrderServiceImpl extends com.baomidou.mybatisplus.extension.service.impl.ServiceImpl<SxscUserCommissionOrderMapper,SxscUserCommissionOrder> implements ISxscUserCommissionOrderService
{

    @Autowired
    private ISxscUserCommissionService iSxscUserCommissionService;

    @Autowired
    private ISxscAliPayWithdrawalService iSxscAliPayWithdrawalService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    SxscUserCommissionOrderMapper sxscUserCommissionOrderMapper;

    @Autowired
    private ISxscUserCommissionQuotaService iSxscUserCommissionQuotaService;

    @Autowired
    private ISxscWeChatTransferService iSxscWeChatTransferService;
    /**
     * 查询佣金提现
     * 
     * @param id 佣金提现主键
     * @return 佣金提现
     */
    @Override
    public SxscUserCommissionOrder selectSxscUserCommissionOrderById(String id)
    {
        return getById(id);
    }

    /**
     * 查询佣金提现总额（包含已经提现到账和申请中和购物）
     *
     * @param userId 用户主键
     * @return 佣金提现
     */
    @Override
    public BigDecimal commissionOrderSum(Long userId)
    {
        return sxscUserCommissionOrderMapper.commissionOrderSum(userId,null);
    }

    /**
     * 查询佣金提现列表
     * 
     * @param sxscUserCommissionOrder 佣金提现
     * @return 佣金提现
     */
    @Override
    public List<SxscUserCommissionOrder> selectSxscUserCommissionOrderList(SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        LambdaQueryWrapper<SxscUserCommissionOrder> wrapper=new LambdaQueryWrapper();

        if(!SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            wrapper.eq(SxscUserCommissionOrder::getUserId,SecurityUtils.getUserId());
        }else{
            wrapper.apply(StringUtils.isNotNull(sxscUserCommissionOrder.getParams().get("phonenumber"))," user_id in (select user_id from sys_user where phonenumber like CONCAT('%','"+sxscUserCommissionOrder.getParams().get("phonenumber")+"', '%'))");
        }
        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionOrder.getExamineStatus()),SxscUserCommissionOrder::getExamineStatus,sxscUserCommissionOrder.getExamineStatus());

        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionOrder.getStatus()),SxscUserCommissionOrder::getStatus,sxscUserCommissionOrder.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscUserCommissionOrder.getPaymentType()),SxscUserCommissionOrder::getPaymentType,sxscUserCommissionOrder.getPaymentType());

        if(StringUtils.isNotNull(sxscUserCommissionOrder.getCreateTime())){
            wrapper.apply("DATE_FORMAT(create_time,'%Y-%m-%d') = '"+ DateUtils.dateTime(sxscUserCommissionOrder.getCreateTime())+"'");
        }

        wrapper.apply( StringUtils.isNotNull(sxscUserCommissionOrder.getCreateDate()),"DATE_FORMAT(create_time,'%Y-%m') = '"+ sxscUserCommissionOrder.getCreateDate()+"'");

        wrapper.orderByDesc(SxscUserCommissionOrder::getCreateTime);

        wrapper.eq(SxscUserCommissionOrder::getDelFlag,0);

        List<SxscUserCommissionOrder> list=list(wrapper);

        for(SxscUserCommissionOrder commissionOrder:list){
            commissionOrder.setSysUser(iSysUserService.selectUserMainById(commissionOrder.getUserId()));
        }

        return list;
    }

    /**
     * 新增佣金提现
     * 
     * @param sxscUserCommissionOrder 佣金提现
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscUserCommissionOrder(SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        if(StringUtils.isNull(sxscUserCommissionOrder.getAmount())){
            return AjaxResult.error("请填写提现金额");
        }
        if(StringUtils.isNull(sxscUserCommissionOrder.getPaymentType())){
            return AjaxResult.error("请选择提现方式");
        }
        BigDecimal usedUserCommission=iSxscUserCommissionService.usedUserCommission(SecurityUtils.getUserId());
        if(usedUserCommission.compareTo(sxscUserCommissionOrder.getAmount())<0){
            return AjaxResult.error("提现金额不足");
        }
        SxscUserInfo userInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());
        if(StringUtils.isNull(userInfo)){
            return AjaxResult.error("用户信息异常");
        }
        //获取提现额度
        BigDecimal commissionQuota=iSxscUserCommissionQuotaService.getCommissionQuotaUserId(SecurityUtils.getUserId());
        //获取当月提现总额
        BigDecimal withdrawal=sxscUserCommissionOrderMapper.commissionOrderSumAmount(SecurityUtils.getUserId(),DateUtils.parseDateToStr(YYYY_MM,DateUtils.getNowDate()));
        if(sxscUserCommissionOrder.getAmount().compareTo(commissionQuota.subtract(withdrawal))>0){
            return AjaxResult.error("提现额度不足");
        }
        sxscUserCommissionOrder.setId(IdUtils.fastSimpleUUID());
        sxscUserCommissionOrder.setStatus(0L);
        sxscUserCommissionOrder.setUserId(SecurityUtils.getUserId());
        sxscUserCommissionOrder.setCreateBy(SecurityUtils.getUsername());
        sxscUserCommissionOrder.setCreateTime(DateUtils.getNowDate());
        save(sxscUserCommissionOrder);
        BigDecimal amount=sxscUserCommissionOrder.getAmount().subtract(sxscUserCommissionOrder.getAmount().multiply(new BigDecimal("0.07")));
        switch (sxscUserCommissionOrder.getPaymentType().intValue()){
            case 1:
                if(StringUtils.isEmpty(userInfo.getAliPayAcc())||StringUtils.isEmpty(userInfo.getAliPayName())){
                    return AjaxResult.error("请完善支付宝信息");
                }
                SxscAliPayWithdrawal sxscAliPayWithdrawal=iSxscAliPayWithdrawalService.insertSxscAliPayWithdrawal(amount,sxscUserCommissionOrder.getUserId(),sxscUserCommissionOrder.getId(),"佣金提现");
                sxscUserCommissionOrder.setExamineUserId(0L);
                sxscUserCommissionOrder.setExamineStatus(1L);
                if(sxscAliPayWithdrawal.getStatus()==1){
                    sxscUserCommissionOrder.setStatus(1L);
                    sxscUserCommissionOrder.setPaymentTime(DateUtils.getNowDate());
                }else {
                    sxscUserCommissionOrder.setStatus(2L);
                }
                updateById(sxscUserCommissionOrder);
                break;
            case 2:
                if(StringUtils.isEmpty(userInfo.getWechatOpenId())||StringUtils.isEmpty(userInfo.getWechatName())){
                    return AjaxResult.error("请绑定微信");
                }
                SxscWeChatTransfer sxscWeChatTransfer=iSxscWeChatTransferService.insertSxscWeChatTransfer(amount,sxscUserCommissionOrder.getUserId(),sxscUserCommissionOrder.getId(),"佣金提现",userInfo.getWechatOpenId(),userInfo.getWechatName());
                sxscUserCommissionOrder.setExamineUserId(0L);
                sxscUserCommissionOrder.setExamineStatus(1L);
                if(sxscWeChatTransfer.getStatus()==1){
                    sxscUserCommissionOrder.setStatus(1L);
                    sxscUserCommissionOrder.setPaymentTime(DateUtils.getNowDate());
                }else {
                    sxscUserCommissionOrder.setStatus(2L);
                }
                updateById(sxscUserCommissionOrder);
            case 3:
                sxscUserCommissionOrder.setStatus(2L);
                updateById(sxscUserCommissionOrder);
                return AjaxResult.error("暂未开放，敬请期待");
            case 4:
                if(StringUtils.isEmpty(userInfo.getPtAddress())){
                    return AjaxResult.error("请完善PT钱包信息");
                }
                sxscUserCommissionOrder.setPtAddress(userInfo.getPtAddress());
                updateById(sxscUserCommissionOrder);
                break;
            default:
                return AjaxResult.error("提现方式暂不支持");
        }
        //佣金提现的时候同步扣除：（提现佣金×1/6）的卖卡额度
        BigDecimal quota=sxscUserCommissionOrder.getAmount().divide(new BigDecimal("6"),2,RoundingMode.UP);

        //iSxscUserConsumeQuotaDetailService.insertSxscUserConsumeQuotaDetail(sxscUserCommissionOrder.getUserId(),roundUpIfFractionalPartExists(quota).multiply(new BigDecimal("-1")),2L);
        return AjaxResult.success();
    }

    private static BigDecimal roundUpIfFractionalPartExists(BigDecimal value) {
        // 检查小数部分是否非零
        if (value.remainder(BigDecimal.ONE).compareTo(BigDecimal.ZERO) != 0) {
            return value.setScale(0, RoundingMode.UP); // 向上进位
        }
        return value; // 否则返回原值
    }

    /**
     * 新增佣金发布预购单
     *
     * @param totalAmount 佣金总额
     * @param orderId 主键
     * @return 结果
     */
    @Override
    public void insertSxscUserCommissionOrder(BigDecimal totalAmount, String orderId){
        SxscUserCommissionOrder sxscUserCommissionOrder=new SxscUserCommissionOrder();
        sxscUserCommissionOrder.setId(orderId);
        sxscUserCommissionOrder.setStatus(4l);
        sxscUserCommissionOrder.setExamineUserId(0l);
        sxscUserCommissionOrder.setExamineStatus(1l);
        sxscUserCommissionOrder.setPaymentTime(DateUtils.getNowDate());
        sxscUserCommissionOrder.setAmount(totalAmount);
        sxscUserCommissionOrder.setDelFlag(0l);
        sxscUserCommissionOrder.setPaymentType(0l);
        sxscUserCommissionOrder.setUserId(SecurityUtils.getUserId());
        sxscUserCommissionOrder.setCreateBy(SecurityUtils.getUsername());
        sxscUserCommissionOrder.setCreateTime(DateUtils.getNowDate());
        save(sxscUserCommissionOrder);
    }



    /**
     * 重新发起佣金提现到账更改为成功状态
     *
     * @param orderId 主键
     * @return 结果
     */
    @Override
    public void updateSxscUserCommissionOrder(String orderId){
        LambdaUpdateWrapper<SxscUserCommissionOrder> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscUserCommissionOrder::getId,orderId);
        updateWrapper.set(SxscUserCommissionOrder::getStatus, 1L);
        updateWrapper.set(SxscUserCommissionOrder::getPaymentTime, DateUtils.getNowDate());
        update(updateWrapper);
    }

    /**
     * 修改佣金提现
     *
     * @param sxscUserCommissionOrder 佣金提现
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscUserCommissionOrder(SxscUserCommissionOrder sxscUserCommissionOrder)
    {
        SxscUserCommissionOrder order=getById(sxscUserCommissionOrder.getId());
        if(StringUtils.isNull(order)){
            return AjaxResult.error("审核数据异常");
        }
        if(sxscUserCommissionOrder.getExamineStatus()==1){
            switch (order.getPaymentType().intValue()){
                case 1:
                    break;
                case 2:
                    break;
                case 3:
                    break;
                case 4:
                    break;
                default:
                    break;
            }
        }else{
            order.setStatus(5L);
        }
        order.setExamineStatus(sxscUserCommissionOrder.getExamineStatus());
        order.setExamineUserId(SecurityUtils.getUserId());
        order.setUpdateBy(SecurityUtils.getUsername());
        order.setUpdateTime(DateUtils.getNowDate());
        updateById(order);
        return AjaxResult.success();
    }

}
