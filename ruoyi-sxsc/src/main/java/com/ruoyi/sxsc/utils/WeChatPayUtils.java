package com.ruoyi.sxsc.utils;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.config.WeChatPayConfig;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.payments.app.AppService;
import com.wechat.pay.java.service.payments.app.model.*;
import com.wechat.pay.java.service.payments.jsapi.JsapiService;
import com.wechat.pay.java.service.payments.jsapi.model.*;
import com.wechat.pay.java.service.payments.nativepay.NativePayService;
import com.wechat.pay.java.service.payments.nativepay.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * 微信支付工具类
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@Slf4j
@Component
public class WeChatPayUtils {

    @Autowired
    private WeChatPayConfig weChatPayConfig;

    /**
     * 获取微信支付配置
     */
    private Config getConfig() {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(weChatPayConfig.getMchId())
                .privateKeyFromPath(weChatPayConfig.getPrivateKeyPath())
                .merchantSerialNumber(weChatPayConfig.getMerchantSerialNumber())
                .apiV3Key(weChatPayConfig.getApiV3Key())
                .build();
    }

    /**
     * APP支付统一下单
     */
    public String appPayOrder(SxscWeChatPayOrder payOrder) {
        try {
            Config config = getConfig();
            AppService service = new AppService.Builder().config(config).build();

            PrepayRequest request = new PrepayRequest();
            request.setAppid(weChatPayConfig.getAppId());
            request.setMchid(weChatPayConfig.getMchId());
            request.setDescription(payOrder.getDescription());
            request.setOutTradeNo(payOrder.getOutTradeNo());
            request.setNotifyUrl(weChatPayConfig.getNotifyUrl());

            Amount amount = new Amount();
            amount.setTotal(payOrder.getTotalAmount().intValue());
            amount.setCurrency(weChatPayConfig.getCurrency());
            request.setAmount(amount);

            PrepayResponse response = service.prepay(request);
            
            // 构建APP调起支付的参数
            PrepayWithRequestPaymentResponse paymentResponse = service.prepayWithRequestPayment(request);
            
            // 保存prepayId
            payOrder.setPrepayId(response.getPrepayId());
            
            return JSONObject.toJSONString(paymentResponse);
        } catch (Exception e) {
            log.error("微信APP支付下单失败", e);
            return null;
        }
    }

    /**
     * JSAPI支付统一下单
     */
    public String jsapiPayOrder(SxscWeChatPayOrder payOrder) {
        try {
            Config config = getConfig();
            JsapiService service = new JsapiService.Builder().config(config).build();

            com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest request = 
                new com.wechat.pay.java.service.payments.jsapi.model.PrepayRequest();
            request.setAppid(weChatPayConfig.getAppId());
            request.setMchid(weChatPayConfig.getMchId());
            request.setDescription(payOrder.getDescription());
            request.setOutTradeNo(payOrder.getOutTradeNo());
            request.setNotifyUrl(weChatPayConfig.getNotifyUrl());

            com.wechat.pay.java.service.payments.jsapi.model.Amount amount = 
                new com.wechat.pay.java.service.payments.jsapi.model.Amount();
            amount.setTotal(payOrder.getTotalAmount().intValue());
            amount.setCurrency(weChatPayConfig.getCurrency());
            request.setAmount(amount);

            Payer payer = new Payer();
            payer.setOpenid(payOrder.getOpenid());
            request.setPayer(payer);

            com.wechat.pay.java.service.payments.jsapi.model.PrepayResponse response = service.prepay(request);
            
            // 构建JSAPI调起支付的参数
            com.wechat.pay.java.service.payments.jsapi.model.PrepayWithRequestPaymentResponse paymentResponse = 
                service.prepayWithRequestPayment(request);
            
            // 保存prepayId
            payOrder.setPrepayId(response.getPrepayId());
            
            return JSONObject.toJSONString(paymentResponse);
        } catch (Exception e) {
            log.error("微信JSAPI支付下单失败", e);
            return null;
        }
    }

    /**
     * Native支付统一下单
     */
    public String nativePayOrder(SxscWeChatPayOrder payOrder) {
        try {
            Config config = getConfig();
            NativePayService service = new NativePayService.Builder().config(config).build();

            com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest request = 
                new com.wechat.pay.java.service.payments.nativepay.model.PrepayRequest();
            request.setAppid(weChatPayConfig.getAppId());
            request.setMchid(weChatPayConfig.getMchId());
            request.setDescription(payOrder.getDescription());
            request.setOutTradeNo(payOrder.getOutTradeNo());
            request.setNotifyUrl(weChatPayConfig.getNotifyUrl());

            com.wechat.pay.java.service.payments.nativepay.model.Amount amount = 
                new com.wechat.pay.java.service.payments.nativepay.model.Amount();
            amount.setTotal(payOrder.getTotalAmount().intValue());
            amount.setCurrency(weChatPayConfig.getCurrency());
            request.setAmount(amount);

            com.wechat.pay.java.service.payments.nativepay.model.PrepayResponse response = service.prepay(request);
            
            // 保存prepayId
            payOrder.setPrepayId(response.getPrepayId());
            
            return response.getCodeUrl();
        } catch (Exception e) {
            log.error("微信Native支付下单失败", e);
            return null;
        }
    }

    /**
     * 根据支付方式调用对应的支付接口
     */
    public String weChatPayOrder(SxscWeChatPayOrder payOrder) {
        if (StringUtils.isEmpty(payOrder.getTradeType())) {
            payOrder.setTradeType("APP");
        }

        switch (payOrder.getTradeType()) {
            case "APP":
                return appPayOrder(payOrder);
            case "JSAPI":
                return jsapiPayOrder(payOrder);
            case "NATIVE":
                return nativePayOrder(payOrder);
            default:
                log.error("不支持的支付方式: {}", payOrder.getTradeType());
                return null;
        }
    }

    /**
     * 查询订单状态
     */
    public JSONObject queryOrder(String outTradeNo) {
        try {
            Config config = getConfig();
            AppService service = new AppService.Builder().config(config).build();
            
            QueryOrderByOutTradeNoRequest request = new QueryOrderByOutTradeNoRequest();
            request.setMchid(weChatPayConfig.getMchId());
            request.setOutTradeNo(outTradeNo);

            Transaction response = service.queryOrderByOutTradeNo(request);
            
            return JSONObject.parseObject(JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("查询微信支付订单失败", e);
            return null;
        }
    }

    /**
     * 关闭订单
     */
    public boolean closeOrder(String outTradeNo) {
        try {
            Config config = getConfig();
            AppService service = new AppService.Builder().config(config).build();
            
            CloseOrderRequest request = new CloseOrderRequest();
            request.setMchid(weChatPayConfig.getMchId());
            request.setOutTradeNo(outTradeNo);

            service.closeOrder(request);
            return true;
        } catch (Exception e) {
            log.error("关闭微信支付订单失败", e);
            return false;
        }
    }

}
