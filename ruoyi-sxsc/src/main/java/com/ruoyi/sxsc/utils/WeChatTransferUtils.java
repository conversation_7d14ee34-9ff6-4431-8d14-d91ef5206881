package com.ruoyi.sxsc.utils;

import com.alibaba.fastjson.JSONObject;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.config.WeChatPayConfig;
import com.ruoyi.sxsc.payment.domain.SxscWeChatTransfer;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.service.transferbatch.TransferBatchService;
import com.wechat.pay.java.service.transferbatch.model.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信转账工具类
 * 
 * <AUTHOR>
 * @date 2024-08-02
 */
@Slf4j
@Component
public class WeChatTransferUtils {

    @Autowired
    private WeChatPayConfig weChatPayConfig;

    /**
     * 获取微信支付配置
     */
    private Config getConfig() {
        return new RSAAutoCertificateConfig.Builder()
                .merchantId(weChatPayConfig.getMchId())
                .privateKeyFromPath(weChatPayConfig.getPrivateKeyPath())
                .merchantSerialNumber(weChatPayConfig.getMerchantSerialNumber())
                .apiV3Key(weChatPayConfig.getApiV3Key())
                .build();
    }

    /**
     * 企业付款到零钱
     */
    public boolean transferToBalance(SxscWeChatTransfer transfer) {
        try {
            Config config = getConfig();
            TransferBatchService service = new TransferBatchService.Builder().config(config).build();

            InitiateBatchTransferRequest request = new InitiateBatchTransferRequest();
            request.setAppid(weChatPayConfig.getAppId());
            request.setOutBatchNo(transfer.getOutBatchNo());
            request.setBatchName("富兴商城转账");
            request.setBatchRemark(transfer.getTransferRemark());
            request.setTotalAmount(transfer.getAmount().longValue());
            request.setTotalNum(1);

            // 构建转账明细
            List<TransferDetailInput> transferDetailList = new ArrayList<>();
            TransferDetailInput detail = new TransferDetailInput();
            detail.setOutDetailNo(transfer.getId());
            detail.setTransferAmount(transfer.getAmount().longValue());
            detail.setTransferRemark(transfer.getTransferRemark());
            detail.setOpenid(transfer.getOpenid());
            
            // 如果有真实姓名，需要进行实名校验
            if (StringUtils.isNotEmpty(transfer.getUserName())) {
                detail.setUserName(transfer.getUserName());
            }
            
            transferDetailList.add(detail);
            request.setTransferDetailList(transferDetailList);

            InitiateBatchTransferResponse response = service.initiateBatchTransfer(request);
            
            // 保存微信返回的批次号
            transfer.setBatchId(response.getBatchId());
            transfer.setResponse(JSONObject.toJSONString(response));
            transfer.setStatus(2L); // 处理中
            transfer.setTransferStatus("PROCESSING");
            
            return true;
        } catch (Exception e) {
            log.error("微信转账到零钱失败", e);
            transfer.setStatus(0L); // 失败
            transfer.setTransferStatus("FAILED");
            transfer.setFailReason(e.getMessage());
            transfer.setResponse(e.getMessage());
            return false;
        }
    }

    /**
     * 查询转账批次
     */
    public JSONObject queryTransferBatch(String outBatchNo) {
        try {
            Config config = getConfig();
            TransferBatchService service = new TransferBatchService.Builder().config(config).build();

            GetTransferBatchByOutBatchNoRequest request = new GetTransferBatchByOutBatchNoRequest();
            request.setOutBatchNo(outBatchNo);
            request.setNeedQueryDetail(true);
            request.setOffset(0);
            request.setLimit(20);
            request.setDetailStatus(GetTransferBatchByOutBatchNoRequest.DetailStatus.ALL);

            TransferBatchEntity response = service.getTransferBatchByOutBatchNo(request);
            
            return JSONObject.parseObject(JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("查询微信转账批次失败", e);
            return null;
        }
    }

    /**
     * 查询转账明细
     */
    public JSONObject queryTransferDetail(String outBatchNo, String outDetailNo) {
        try {
            Config config = getConfig();
            TransferBatchService service = new TransferBatchService.Builder().config(config).build();

            GetTransferDetailByOutBatchNoRequest request = new GetTransferDetailByOutBatchNoRequest();
            request.setOutBatchNo(outBatchNo);
            request.setOutDetailNo(outDetailNo);

            TransferDetailEntity response = service.getTransferDetailByOutBatchNo(request);
            
            return JSONObject.parseObject(JSONObject.toJSONString(response));
        } catch (Exception e) {
            log.error("查询微信转账明细失败", e);
            return null;
        }
    }

    /**
     * 更新转账状态
     */
    public void updateTransferStatus(SxscWeChatTransfer transfer) {
        try {
            JSONObject batchInfo = queryTransferBatch(transfer.getOutBatchNo());
            if (batchInfo != null) {
                String batchStatus = batchInfo.getString("batch_status");
                transfer.setTransferStatus(batchStatus);
                
                switch (batchStatus) {
                    case "ACCEPTED":
                    case "PROCESSING":
                        transfer.setStatus(2L); // 处理中
                        break;
                    case "CLOSED":
                        transfer.setStatus(1L); // 成功
                        break;
                    case "FAILED":
                        transfer.setStatus(0L); // 失败
                        transfer.setFailReason(batchInfo.getString("fail_reason"));
                        break;
                }

                // 查询转账明细获取更详细的信息
                JSONObject detailInfo = queryTransferDetail(transfer.getOutBatchNo(), transfer.getId());
                if (detailInfo != null) {
                    transfer.setDetailId(detailInfo.getString("detail_id"));
                    String detailStatus = detailInfo.getString("detail_status");
                    
                    switch (detailStatus) {
                        case "PROCESSING":
                            transfer.setStatus(2L); // 处理中
                            break;
                        case "SUCCESS":
                            transfer.setStatus(1L); // 成功
                            break;
                        case "FAILED":
                            transfer.setStatus(0L); // 失败
                            transfer.setFailReason(detailInfo.getString("fail_reason"));
                            break;
                    }
                }
            }
        } catch (Exception e) {
            log.error("更新微信转账状态失败", e);
        }
    }

}
