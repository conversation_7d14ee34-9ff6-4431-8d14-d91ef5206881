package com.ruoyi.sxsc.commodity.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityEvaluateService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityOrderService;
import com.ruoyi.sxsc.commodity.service.ISxscCommodityService;
import com.ruoyi.sxsc.dingdong.service.ISxscDingdongCommodityMappingService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 商品管理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@RestController
@RequestMapping("/commodity/info")
public class SxscCommodityController extends BaseController
{
    @Autowired
    private ISxscCommodityService sxscCommodityService;

    @Autowired
    private ISxscCommodityEvaluateService iSxscCommodityEvaluateService;

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscDingdongCommodityMappingService iSxscDingdongCommodityMappingService;

    /**
     * 查询商品管理列表
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(SxscCommodity sxscCommodity)
    {
        startPage();
        List<SxscCommodity> list = sxscCommodityService.selectSxscCommodityList(sxscCommodity);
        return getDataTable(list);
    }

    /**
     * 获取商品管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityService.selectSxscCommodityById(id));
    }

    /**
     * 获取商品管理待支付凭证(支付宝)
     */
    @GetMapping(value = "/pay/{id}")
    public AjaxResult getPayInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityService.selectSxscCommodityPayById(id));
    }

    /**
     * 获取商品管理待支付凭证(微信支付)
     */
    @GetMapping(value = "/wechatpay/{id}")
    public AjaxResult getWeChatPayInfo(@PathVariable("id") Long id)
    {
        return success(sxscCommodityService.selectSxscCommodityWeChatPayById(id));
    }

    /**
     * 新增商品管理
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:add')")
    @Log(title = "商品管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SxscCommodity sxscCommodity)
    {
        return sxscCommodityService.insertSxscCommodity(sxscCommodity);
    }

    /**
     * 修改商品管理
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:edit')")
    @Log(title = "商品管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SxscCommodity sxscCommodity)
    {
        return sxscCommodityService.updateSxscCommodity(sxscCommodity);
    }

    /**
     * 修改商品排序
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:sortNumber')")
    @Log(title = "商品管理排序", businessType = BusinessType.UPDATE)
    @PutMapping("/sortNumber")
    public AjaxResult editSortNumber(@RequestBody Map<String,Long> map)
    {
        return sxscCommodityService.updateSxscCommoditySortNumber(map);
    }

    /**
     * 商品信息上架
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:edit')")
    @GetMapping(value = "/shelf/{id}")
    @Log(title = "商品信息上架", businessType = BusinessType.UPDATE)
    public AjaxResult shelf(@PathVariable("id") Long id)
    {
        return sxscCommodityService.updateCommodityShelf(id);
    }

    /**
     * 商品信息下架
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:edit')")
    @GetMapping(value = "/offShel/{id}")
    @Log(title = "商品信息下架", businessType = BusinessType.UPDATE)
    public AjaxResult offShel(@PathVariable("id") Long id)
    {
        return sxscCommodityService.updateCommodityOffShel(id);
    }


    /**
     * 商品信息置顶
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:topUpTime')")
    @GetMapping(value = "/topUpTime/{id}")
    @Log(title = "商品信息置顶", businessType = BusinessType.UPDATE)
    public AjaxResult topUpTime(@PathVariable("id") Long id)
    {
        return sxscCommodityService.updateCommodityTopUpTime(id);
    }

    /**
     * 商品信息取消置顶
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:topUpTime')")
    @GetMapping(value = "/cancelTopUpTime/{id}")
    @Log(title = "商品信息取消置顶", businessType = BusinessType.UPDATE)
    public AjaxResult cancelTopUpTime(@PathVariable("id") Long id)
    {
        return sxscCommodityService.updateCommodityCancelTopUpTime(id);
    }

    /**
     * 删除商品管理
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:remove')")
    @Log(title = "商品管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{id}/{refundType}")
    public AjaxResult remove(@PathVariable Long id,@PathVariable Integer refundType)
    {
        return sxscCommodityService.deleteSxscCommodityById(id,refundType);
    }

    /**
     * 查询附近的商品
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:query')")
    @GetMapping("/vicinity/list")
    public AjaxResult vicinity(@RequestParam(value = "longitude")String longitude,
                               @RequestParam(value = "latitude")String latitude)
    {

        List<SxscCommodity> list = sxscCommodityService.selectCommodityVicinityList(longitude,latitude);
        return AjaxResult.success(list);
    }

    /**
     * 获取商品评价信息好评率
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:query')")
    @GetMapping(value = "/rate/{commodityId}")
    public AjaxResult getRate(@PathVariable("commodityId") Long commodityId)
    {
        return success(iSxscCommodityEvaluateService.getRate(commodityId));
    }

    /**
     * 获取每日爆品
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:query')")
    @GetMapping(value = "/explosive-products")
    public AjaxResult explosiveProducts()
    {
        Long commodityIds=iSxscCommodityOrderService.explosiveProducts();
        if(StringUtils.isNull(commodityIds)){
            return AjaxResult.success(new ArrayList<>());
        }
        LambdaQueryWrapper<SxscCommodity> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        lambdaQueryWrapper.in(SxscCommodity::getId,commodityIds);
        return success(sxscCommodityService.list(lambdaQueryWrapper));
    }

    /**
     * 同步供应连最新商品数据
     */
    @PreAuthorize("@ss.hasPermi('commodity:info:edit')")
    @GetMapping(value = "/dingdongSync/{id}")
    public AjaxResult dingdongSync(@PathVariable Long id)
    {

        return iSxscDingdongCommodityMappingService.proactivelyInitiateNotice(id);
    }
}
