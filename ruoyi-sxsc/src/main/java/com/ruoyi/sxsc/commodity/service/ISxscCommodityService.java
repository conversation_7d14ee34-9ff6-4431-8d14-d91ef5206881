package com.ruoyi.sxsc.commodity.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;

import java.util.List;
import java.util.Map;

/**
 * 商品管理Service接口
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
public interface ISxscCommodityService extends IService<SxscCommodity>
{
    /**
     * 查询商品管理
     * 
     * @param id 商品管理主键
     * @return 商品管理
     */
    SxscCommodity selectSxscCommodityById(Long id);

    /**
     * 查询商品待支付凭证
     *
     * @param id 商品管理主键
     * @return 商品管理
     */
    AjaxResult selectSxscCommodityPayById(Long id);

    /**
     * 查询商品待支付凭证(微信支付)
     *
     * @param id 商品管理主键
     * @return 商品管理
     */
    AjaxResult selectSxscCommodityWeChatPayById(Long id);

    /**
     * 查询商品管理列表
     * 
     * @param sxscCommodity 商品管理
     * @return 商品管理集合
     */
    List<SxscCommodity> selectSxscCommodityList(SxscCommodity sxscCommodity);

    /**
     * 新增商品管理
     * 
     * @param sxscCommodity 商品管理
     * @return 结果
     */
    AjaxResult insertSxscCommodity(SxscCommodity sxscCommodity);

    /**
     * 修改商品管理
     * 
     * @param sxscCommodity 商品管理
     * @return 结果
     */
    AjaxResult updateSxscCommodity(SxscCommodity sxscCommodity);


    /**
     * 修改商品排序
     *
     * @param map 商品排序信息
     * @return 结果
     */
    AjaxResult updateSxscCommoditySortNumber(Map<String,Long> map);
    /**
     * 修改商品排序
     *
     * @param id 主键
     * @param sortNumber 排序
     * @return 结果
     */
    AjaxResult updateSxscCommoditySortNumber(Long id,Long sortNumber);
    /**
     * 修改商品支付状态
     *
     * @param payOrderId 支付订单主键
     * @return 结果
     */
    AjaxResult updateSxscCommodityPayStatus(String payOrderId);
    /**
     * 商品信息状态修改
     *
     * @param id 商品主键
     * @return 结果
     */
    void updateCommodityStatus(Long id,Long status);

    /**
     * 商品信息上架
     *
     * @param id 商品主键
     * @return 结果
     */
    AjaxResult updateCommodityShelf(Long id);


    /**
     * 商品信息下架
     *
     * @param id 商品主键
     * @return 结果
     */
    AjaxResult updateCommodityOffShel(Long id);

    /**
     * 商品信息置顶
     *
     * @param id 商品主键
     * @return 结果
     */
    AjaxResult updateCommodityTopUpTime(Long id);

    /**
     * 商品信息取消置顶
     *
     * @param id 商品主键
     * @return 结果
     */
    AjaxResult updateCommodityCancelTopUpTime(Long id);

    /**
     * 删除商品管理信息
     * 
     * @param id 商品管理主键
     * @param refundType 是否退款1退
     * @return 结果
     */
    AjaxResult deleteSxscCommodityById(Long id,Integer refundType);

    /**
     * 查询附近的商品
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return 结果
     */

    List<SxscCommodity> selectCommodityVicinityList(String longitude,String latitude);
}
