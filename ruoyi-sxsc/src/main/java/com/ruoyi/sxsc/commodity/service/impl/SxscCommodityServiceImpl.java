package com.ruoyi.sxsc.commodity.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.uuid.IdUtils;
import com.ruoyi.sxsc.commodity.domain.SxscCommodity;
import com.ruoyi.sxsc.commodity.domain.SxscCommoditySpecifications;
import com.ruoyi.sxsc.commodity.domain.SxscCommodityType;
import com.ruoyi.sxsc.commodity.mapper.SxscCommodityMapper;
import com.ruoyi.sxsc.commodity.service.*;
import com.ruoyi.sxsc.enterprise.domain.SxscEnterprise;
import com.ruoyi.sxsc.enterprise.mapper.SxscEnterpriseMapper;
import com.ruoyi.sxsc.enterprise.service.ISxscEnterpriseService;
import com.ruoyi.sxsc.payment.domain.SxscAliPayOrder;
import com.ruoyi.sxsc.payment.domain.SxscWeChatPayOrder;
import com.ruoyi.sxsc.payment.service.ISxscAliPayOrderService;
import com.ruoyi.sxsc.payment.service.ISxscWeChatPayOrderService;
import com.ruoyi.sxsc.person.domain.SxscUserInfo;
import com.ruoyi.sxsc.person.service.ISxscUserInfoService;
import com.ruoyi.system.service.ISysConfigService;
import com.ruoyi.system.service.ISysUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 商品管理Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-05-10
 */
@Service
public class SxscCommodityServiceImpl extends ServiceImpl<SxscCommodityMapper,SxscCommodity> implements ISxscCommodityService
{

    @Autowired
    private ISxscCommodityTypeService iSxscCommodityTypeService;

    @Autowired
    private ISxscCommoditySpecificationsService iSxscCommoditySpecificationsService;

    @Autowired
    private ISxscCommodityOperationService iSxscCommodityOperationService;

    @Autowired
    private ISxscCommodityPlateService iSxscCommodityPlateService;

    @Autowired
    private ISysUserService iSysUserService;

    @Autowired
    private ISxscEnterpriseService iSxscEnterpriseService;

    @Autowired
    private ISxscCommodityOrderService iSxscCommodityOrderService;

    @Autowired
    private ISxscUserInfoService iSxscUserInfoService;

    @Autowired
    private SxscEnterpriseMapper sxscEnterpriseMapper;

    @Autowired
    private SxscCommodityMapper sxscCommodityMapper;

    @Autowired
    private ISxscAliPayOrderService iSxscAliPayOrderService;

    @Autowired
    private ISxscCommodityOrderRefundService iSxscCommodityOrderRefundService;

    @Autowired
    private ISysConfigService iSysConfigService;

    @Autowired
    private ISxscWeChatPayOrderService iSxscWeChatPayOrderService;

    /**
     * 查询商品管理
     * 
     * @param id 商品管理主键
     * @return 商品管理
     */
    @Override
    public SxscCommodity selectSxscCommodityById(Long id)
    {
        SxscCommodity sxscCommodity=getById(id);
        sxscCommodity.setSpecifications(iSxscCommoditySpecificationsService.selectSxscCommoditySpecificationsList(new SxscCommoditySpecifications(id)));
        SxscCommodityType sxscCommodityType=iSxscCommodityTypeService.getById(sxscCommodity.getType());
        if(StringUtils.isNotNull(sxscCommodityType)){
            sxscCommodity.setTypeName(sxscCommodityType.getName());
        }
        sxscCommodity.setCommodityPlate(iSxscCommodityPlateService.getById(sxscCommodity.getPlateId()));
        sxscCommodity.setSysUser(iSysUserService.selectUserMainById(sxscCommodity.getUserId()));
        sxscCommodity.setEnterprise(iSxscEnterpriseService.selectEnterpriseByUserId(sxscCommodity.getUserId()));
        sxscCommodity.setTotalSales(iSxscCommodityOrderService.getCommodityOrderTotalSales(sxscCommodity.getId()));
        sxscCommodity.setAmountSales(iSxscCommodityOrderService.getCommodityOrderAmountSales(sxscCommodity.getId()));
        return sxscCommodity;
    }

    /**
     * 查询商品待支付凭证
     *
     * @param id 商品管理主键
     * @return 商品管理
     */
    @Override
    public AjaxResult selectSxscCommodityPayById(Long id){
        SxscCommodity sxscCommodity=getById(id);
        if(StringUtils.isNull(sxscCommodity)){
            return  AjaxResult.error("商品信息不存在");
        }
        String aliPayStr=iSxscAliPayOrderService.insertSxscAliPayOrder(new SxscAliPayOrder(sxscCommodity));
        return AjaxResult.success("操作成功",aliPayStr);
    }

    /**
     * 查询商品待支付凭证(微信支付)
     *
     * @param id 商品管理主键
     * @return 商品管理
     */
    @Override
    public AjaxResult selectSxscCommodityWeChatPayById(Long id){
        SxscCommodity sxscCommodity=getById(id);
        if(StringUtils.isNull(sxscCommodity)){
            return  AjaxResult.error("商品信息不存在");
        }
        String weChatPayStr=iSxscWeChatPayOrderService.insertSxscWeChatPayOrder(new SxscWeChatPayOrder(sxscCommodity));
        return AjaxResult.success("操作成功",weChatPayStr);
    }

    /**
     * 查询商品管理列表
     * 
     * @param sxscCommodity 商品管理
     * @return 商品管理
     */
    @Override
    public List<SxscCommodity> selectSxscCommodityList(SxscCommodity sxscCommodity)
    {
//        板块主键
//        普通商品：0
//        超值购商品：根据查询专区id查询
//        品牌购商品：2
//        拼团购商品：3
//        新人特惠：4
        LambdaQueryWrapper<SxscCommodity> wrapper=new LambdaQueryWrapper();

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getUserId()),SxscCommodity::getUserId,sxscCommodity.getUserId());

        wrapper.like(StringUtils.isNotNull(sxscCommodity.getName()),SxscCommodity::getName,sxscCommodity.getName());

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getType()),SxscCommodity::getType,sxscCommodity.getType());

        wrapper.like(StringUtils.isNotNull(sxscCommodity.getDetails()),SxscCommodity::getDetails,sxscCommodity.getDetails());

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getStatus()),SxscCommodity::getStatus,sxscCommodity.getStatus());

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getPlateId()),SxscCommodity::getPlateId,sxscCommodity.getPlateId());

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getCreateBy()),SxscCommodity::getCreateBy,sxscCommodity.getCreateBy());

        wrapper.eq(StringUtils.isNotNull(sxscCommodity.getSourceType()),SxscCommodity::getSourceType,sxscCommodity.getSourceType());

        //富星超值购
        wrapper.apply(StringUtils.isNotNull(sxscCommodity.getParams().get("benefitRights"))," plate_id in (select id from sxsc_commodity_plate where parent_id!=0 ) ");

        wrapper.orderByDesc(SxscCommodity::getTopUpTime);

        wrapper.orderByAsc(SxscCommodity::getSortNumber);

        List<SxscCommodity> list=list(wrapper);


        return list;
    }

    /**
     * 新增商品管理
     * 
     * @param sxscCommodity 商品管理
     * @return 结果
     */
    @Override
    public AjaxResult insertSxscCommodity(SxscCommodity sxscCommodity)
    {
        if(StringUtils.isNull(sxscCommodity.getSpecifications())||sxscCommodity.getSpecifications().size()==0){
            return AjaxResult.error("商品缺少规格信息，无法新增商品");
        }
        if(StringUtils.isNotNull(sxscCommodity.getType())){
            SxscCommodityType xgfmCommodityType=iSxscCommodityTypeService.selectSxscCommodityTypeById(sxscCommodity.getType());
            if(StringUtils.isNull(xgfmCommodityType)){
                return AjaxResult.error("商品类型不存在，无法新增商品");
            }
        }

        if(sxscCommodity.getFreightType()==1){
            sxscCommodity.setFreight(new BigDecimal(0));
        } else if (StringUtils.isNull(sxscCommodity.getFreight())) {
            return AjaxResult.error("请填写运费");
        }
        //所有商品都需要校验
        SxscUserInfo sxscUserInfo=iSxscUserInfoService.getById(SecurityUtils.getUserId());
        if(StringUtils.isNull(sxscUserInfo)){
            return AjaxResult.error("账号信息异常");
        }
        if(StringUtils.isEmpty(sxscUserInfo.getAliPayAcc())||StringUtils.isEmpty(sxscUserInfo.getAliPayAcc())){
            return AjaxResult.error("请先完成支付宝账户信息认证");
        }
        if(StringUtils.isEmpty(sxscUserInfo.getWechatOpenId())){
            return AjaxResult.error("请先绑定微信账号");
        }
        for(SxscCommoditySpecifications specifications:sxscCommodity.getSpecifications()){
            if(StringUtils.isEmpty(specifications.getImgUrl())
            ||StringUtils.isEmpty(specifications.getName())
            ||StringUtils.isNull(specifications.getPrice())
            ||StringUtils.isNull(specifications.getSupplyAmount())){
                return AjaxResult.error("商品规格明细数据有误，请检查");
            }
        }
        if(StringUtils.isNotNull(sxscCommodity.getProductNature())&&sxscCommodity.getProductNature()==2){
            sxscCommodity.setPlateId(2l);
        }
        sxscCommodity.setStatus(-1l);
        sxscCommodity.setCreateBy(SecurityUtils.getUsername());
        sxscCommodity.setCreateTime(DateUtils.getNowDate());
        sxscCommodity.setUserId(SecurityUtils.getUserId());
        sxscCommodity.setSourceType(1l);
        sxscCommodity.setPayType(1l);
        sxscCommodity.setPayOrderId(IdUtils.fastSimpleUUID());
        save(sxscCommodity);
        iSxscCommoditySpecificationsService.insertSxscCommoditySpecifications(sxscCommodity.getSpecifications(),sxscCommodity.getId());
        String aliPayStr=iSxscAliPayOrderService.insertSxscAliPayOrder(new SxscAliPayOrder(sxscCommodity));
        return AjaxResult.success("操作成功",aliPayStr);
    }

    /**
     * 修改商品管理
     * 
     * @param sxscCommodity 商品管理
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscCommodity(SxscCommodity sxscCommodity)
    {
        SxscCommodity sxscCommodityData=getById(sxscCommodity.getId());
        if(StringUtils.isNull(sxscCommodityData)){
            return AjaxResult.error("商品信息有误，无法修改");
        }
        if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            sxscCommodity.setStatus(sxscCommodityData.getStatus());
        }else{
            if(sxscCommodityData.getStatus()!=0&&sxscCommodityData.getStatus()!=5&&sxscCommodityData.getSourceType()==1){
                return AjaxResult.error("商品信息不属于修改期间，无法修改");
            }
            sxscCommodity.setStatus(-1l);
        }
        if(StringUtils.isNull(sxscCommodity.getSpecifications())||sxscCommodity.getSpecifications().size()==0){
            return AjaxResult.error("商品缺少规格信息，无法修改");
        }
        if(StringUtils.isNotNull(sxscCommodity.getType())){
            SxscCommodityType xgfmCommodityType=iSxscCommodityTypeService.selectSxscCommodityTypeById(sxscCommodity.getType());
            if(StringUtils.isNull(xgfmCommodityType)){
                return AjaxResult.error("商品类型不存在，无法新增商品");
            }
        }
        if(sxscCommodity.getFreightType()==1){
            sxscCommodity.setFreight(new BigDecimal(0));
        }
        if(SecurityUtils.getLoginUser().getUser().getUserType().equals("00")){
            sxscCommodity.setStatus(sxscCommodityData.getStatus());
        }else{
            sxscCommodity.setStatus(-1l);
        }
        iSxscCommoditySpecificationsService.insertSxscCommoditySpecifications(sxscCommodity.getSpecifications(),sxscCommodity.getId());
        sxscCommodity.setUpdateBy(SecurityUtils.getUsername());
        sxscCommodity.setUpdateTime(DateUtils.getNowDate());
        updateById(sxscCommodity);
        iSxscCommodityOperationService.insertSxscCommodityOperation(sxscCommodityData.getId(),10L);
        return AjaxResult.success();
    }

    /**
     * 修改商品排序
     *
     * @param map 商品排序信息
     * @return 结果
     */
    @Override
    @Transactional
    public AjaxResult updateSxscCommoditySortNumber(Map<String,Long> map){
        if (StringUtils.isNull(map.get("id"))) {
            return AjaxResult.error("商品标识为空");
        }
        if (StringUtils.isNull(map.get("oldIndex"))) {
            return AjaxResult.error("商品旧排序参数为空");
        }
        if (StringUtils.isNull(map.get("newIndex"))) {
            return AjaxResult.error("商品新排序参数为空");
        }
        //新sort比旧sort大,下拖,区间内加
        if (map.get("oldIndex") > map.get("newIndex")) {
            sxscCommodityMapper.updateForward(map.get("oldIndex"),map.get("newIndex"));
        } else {
            //新sort比旧sort小,上拉,区间内减
            sxscCommodityMapper.updateBackwards(map.get("oldIndex"), map.get("newIndex"));
        }
        sxscCommodityMapper.updateConfirm(map.get("id"), map.get("newIndex"));
        return AjaxResult.success();
    }

    /**
     * 修改商品排序
     *
     * @param id 主键
     * @param sortNumber 排序
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscCommoditySortNumber(Long id,Long sortNumber){
        LambdaUpdateWrapper<SxscCommodity> wrapper=new LambdaUpdateWrapper<>();
        wrapper.eq(SxscCommodity::getId,id);
        wrapper.set(SxscCommodity::getSortNumber,sortNumber);
        wrapper.set(SxscCommodity::getUpdateBy,SecurityUtils.getUsername());
        wrapper.set(SxscCommodity::getUpdateTime,DateUtils.getNowDate());
        update(wrapper);
        return AjaxResult.success();
    }

    /**
     * 修改商品支付状态
     *
     * @param payOrderId 支付订单主键
     * @return 结果
     */
    @Override
    public AjaxResult updateSxscCommodityPayStatus(String payOrderId){
        LambdaQueryWrapper<SxscCommodity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(SxscCommodity::getPayOrderId,payOrderId);
        SxscCommodity sxscCommodityData=getOne(queryWrapper);
        if(StringUtils.isNotNull(sxscCommodityData)){
            LambdaUpdateWrapper<SxscCommodity> wrapper=new LambdaUpdateWrapper<>();
            wrapper.eq(SxscCommodity::getId,sxscCommodityData.getId());
            wrapper.set(SxscCommodity::getStatus,0);
            wrapper.set(SxscCommodity::getUpdateTime,DateUtils.getNowDate());
            update(wrapper);
            iSxscCommodityOperationService.insertSxscCommodityOperation(sxscCommodityData.getId(),9L);
        }
        return AjaxResult.success();
    }
    /**
     * 商品信息状态修改
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public void updateCommodityStatus(Long id,Long status){
        LambdaUpdateWrapper<SxscCommodity> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommodity::getId,id);
        updateWrapper.set(SxscCommodity::getStatus,status);
        updateWrapper.set(SxscCommodity::getUpdateTime,DateUtils.getNowDate());
        update(updateWrapper);
    }

    /**
     * 商品信息上架
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityShelf(Long id){
        SxscCommodity sxscCommodityData=getById(id);
        if(StringUtils.isNull(sxscCommodityData)){
            return AjaxResult.error("商品信息有误，无法上架");
        }
        updateCommodityStatus(id,2l);
        iSxscCommodityOperationService.insertSxscCommodityOperation(id,2L);
        return AjaxResult.success();
    }


    /**
     * 商品信息下架
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityOffShel(Long id){
        SxscCommodity commodityData=getById(id);
        if(StringUtils.isNull(commodityData)){
            return AjaxResult.error("商品信息有误，无法下架");
        }
        if(commodityData.getStatus()!=2){
            return AjaxResult.error("商品信息未上架，无法下架");
        }
        updateCommodityStatus(id,3l);
        iSxscCommodityOperationService.insertSxscCommodityOperation(id,3L);
        return AjaxResult.success();
    }

    /**
     * 商品信息置顶
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityTopUpTime(Long id){
        LambdaUpdateWrapper<SxscCommodity> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommodity::getId,id);
        updateWrapper.set(SxscCommodity::getTopUpTime,DateUtils.getNowDate());
        update(updateWrapper);
        iSxscCommodityOperationService.insertSxscCommodityOperation(id,7L);
        return AjaxResult.success();
    }

    /**
     * 商品信息取消置顶
     *
     * @param id 商品主键
     * @return 结果
     */
    @Override
    public AjaxResult updateCommodityCancelTopUpTime(Long id){
        LambdaUpdateWrapper<SxscCommodity> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommodity::getId,id);
        updateWrapper.set(SxscCommodity::getTopUpTime,null);
        update(updateWrapper);
        iSxscCommodityOperationService.insertSxscCommodityOperation(id,8L);
        return AjaxResult.success();
    }

    /**
     * 删除商品管理信息
     * 
     * @param id 商品管理主键
     * @param refundType 是否退款1退
     * @return 结果
     */
    @Override
    public AjaxResult deleteSxscCommodityById(Long id,Integer refundType)
    {
        SxscCommodity sxscCommodity=getById(id);
        if(StringUtils.isNull(sxscCommodity)||sxscCommodity.getStatus()==4){
            return AjaxResult.error("商品信息有误，无法删除");
        }
        LambdaUpdateWrapper<SxscCommodity> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(SxscCommodity::getId,id);
        updateWrapper.set(SxscCommodity::getStatus,4);
        update(updateWrapper);
        iSxscCommodityOperationService.insertSxscCommodityOperation(id,4L);
        if(refundType==1){
            if(StringUtils.isEmpty(sxscCommodity.getPayOrderId())||sxscCommodity.getStatus()==-1){
                return AjaxResult.error("该商品未支付保证金");
            }
            iSxscCommodityOrderRefundService.insertSxscCommodityOrderRefund(sxscCommodity);
        }
        return AjaxResult.success();
    }


    /**
     * 查询附近的商品
     *
     * @param longitude 经度
     * @param latitude 纬度
     * @return 结果
     */
    @Override
    public List<SxscCommodity> selectCommodityVicinityList(String longitude,String latitude){
        List<SxscEnterprise> enterprises= sxscEnterpriseMapper.nearby(longitude,latitude);
        enterprises.stream().map(SxscEnterprise::getUserId).collect(Collectors.toList());
        List<Long> userIds=null;
        if(StringUtils.isNull(userIds)||userIds.size()==0){
            return new ArrayList<>();
        }
        LambdaQueryWrapper<SxscCommodity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.in(SxscCommodity::getUserId,userIds);
        queryWrapper.eq(SxscCommodity::getStatus,2);
        queryWrapper.orderByAsc(SxscCommodity::getSortNumber);
        queryWrapper.last("limit 10");
        return list(queryWrapper);
    }
}
