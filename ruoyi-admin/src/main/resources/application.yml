# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2023
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: /www/wwwroot/hcyx/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8899
  port: 8899
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: local
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 10MB
      # 设置总上传的文件大小
      max-request-size: 20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# token配置
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 01f8971e881b42288da6ad95e58a5916
  # 令牌有效期（默认30分钟）
  expireTime: 2880

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
  #为每个语句的执行创建一个新的预处理语句
  executorType: SIMPLE
  global-config:
    db-config:
      #驼峰下划线转换
      tableUnderline: true
      select-strategy: NOT_EMPTY
      insert-strategy: NOT_EMPTY
      update-strategy: NOT_NULL

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

ali:
  oss:
    accessKeyId: "LTAI5tL3cVf1t7DW2ZTbpung"
    accessKeySecret: "******************************"
    bucketName: "hcyx-ht"
    objectPrefixName: "img/"
    endpoint_inter: "oss-cn-beijing-internal.aliyuncs.com"
    endpoint: "oss-cn-beijing.aliyuncs.com"
  access:
    accessKeyId: "LTAI5tL3cVf1t7DW2ZTbpung"
    accessKeySecret: "******************************"
  sms:
    accessKeyId: "LTAI5tL3cVf1t7DW2ZTbpung"
    accessKeySecret: "******************************"
    templateCode: "SMS_485670132"
    signName: "湖北惠创联恒电子商务"
  pay:
    app_id: "2021005146619804"
    merchant_private_key: "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDAWuaizxxaYpWs+AZReM0GoaQlFUmsxSAVCISSUCBzzyY7rAsCe5UtaWiCHLLKrHQHNX00Dk0O56AqH25J5c1i3ximPgdC0106SMVtYxsREAY8wHFHlSd11ZNYsdBOFbl7uXYFHlF20iJgWQzDWXIjLu9Dscg4Z0nMOwKp00a33k7MwhkgxiU/BgtTiuVtjNBsVGE7bXBbWbUFqPvNrYUqHG9bkUrXPKhnfSpurt3mYk8JB2oLWUM1tJlJcPzpDlveS4dm99sksXOQGUikKyk8RITIIdlTxxC7qOTZHWB0qhzA450SA2Jhb8vlsZ1SwfvFHGzqqVwE8/gp8NbkGXg1AgMBAAECggEANoKMEV833bVKTTYMNFJTZ6mu5oNd2YQTT2KWjw/94lO1tftPM2jqIXBZmWWglSbByrkxveq/k5W4Ibe+fVqOFZK7XYD+dcWICdhNem43YWJ1Igo3D94IzK+1jdD2AurNIW6xL0okM9PgwgT6RPG4EpheXYSoBLPgieu/GXkx9TCfyBJvDt2dGphbBbHdQ1tbcOoCnR0b9tu4ODeWC2MbUO03hLQ1V4vB5RZzJdRkpxYoDKKYAeVIaeAZCpvsXeVadHhYnIvMJNT3LC1uMABD+8P9zZyTcYfWr4Np1cmRlrrlnfHrrZLGTawcWaJJIy6msgWUI/EVYKr5hxiZ8R9BkQKBgQD7n8tnX9TCBFjkqILLHitDmNhyMmSS4fGho8vsuVN6ZEjRENW+p9BLHUWXNpOnVsLffLCjI1e9Q5iTjI6UcEUkpRCwMJjQ5wkYImLHH06giQ6qy/kozTV9QEhTmKB8cS1TMaqufb4NJKhvFovWJ8Bw1Yzk8o1t0dsjDyxzLb8FowKBgQDDsz8LuA6AEBc2PlhJNvJpNDqohwalEd0y69+egR7l8OGP0dJKdzI6+7KU+szwmYI5pB8il4sq+09swEoibE0nNXEirqyNg0ezzAsp1PD9bJScJ5LsbicovwBHrtMiF+++InGlMvnMGyrNJ2KcXJOvNN0iRqmA5PaeDK2Mb0P4RwKBgEgVzDjOPG5kvm6rU+UKvIw+AmwcwbFOp2GOQkKMCT0785xpPULEYqjSNviN+xjqgyNxXk0uN9mRvukuUD54N2a7Hx/KnEzkkzdlynSPJIBbI2eMzdGBkHxUaSQByqA78CcwSFx6y3KKePJyPBdbBgeNNjL0xc2bXDMcCrjodxwPAoGAQK9YoWj+8flfksBcsiaFTZzD3rxyx9D1ykUIrv2C1wIdrEnwNkjbw386cd/MKaKz1pWW7PomN9jFUe7lEie2AMW7VXli84t3Acjpz7PgzAM9w7eY6qkmLGOllgwFIXnD9pav9ai5xGseZJ0mDQTUk87nYNL2Z/JGSctq+cY1MzUCgYAa77xsIf1ihQCPherpoa90rIlYffjtBrzzT54bpWXMvC6JVtt9TjOedyVc4H0GIduxyktbp6lFip6zOvWEtD+sN2HqY2an6T4meQ2p2P4TxCyhjX2wjbt5GJrb2I3KCo1NwwGnIeKGuVKk9DSkcpkBO8hDRN7WvfYQgV6HUFynJw=="
    notify_url: "https://hcyx.hbhclh.cn/prod-api/ali/payment/order/ali-notify"
    return_url: "localhost:8080/alipay.trade.page.pay-JAVA-UTF-8/return_url.jsp"
    sign_type: "RSA2"
    charset: "utf-8"
    format: "json"
    gatewayUrl: "https://openapi.alipay.com/gateway.do"
    timeout_express: "15d"

wechat:
  pay:
    appId: "wx1234567890abcdef"
    mchId: "1234567890"
    privateKey: "-----BEGIN PRIVATE KEY-----\nMIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...\n-----END PRIVATE KEY-----"
    merchantSerialNumber: "1234567890ABCDEF1234567890ABCDEF12345678"
    apiV3Key: "your_api_v3_key_32_characters_long"
    notifyUrl: "https://hcyx.hbhclh.cn/prod-api/wechat/payment/order/wechat-notify"
    refundNotifyUrl: "https://hcyx.hbhclh.cn/prod-api/wechat/payment/refund/wechat-notify"
    transferNotifyUrl: "https://hcyx.hbhclh.cn/prod-api/wechat/payment/transfer/wechat-notify"
    certPath: "/path/to/apiclient_cert.pem"
    privateKeyPath: "/path/to/apiclient_key.pem"
    wechatPayCertPath: "/path/to/wechatpay_cert.pem"
    environment: "production"
    timeoutExpress: "15d"
    currency: "CNY"
    signType: "RSA"
    charset: "utf-8"
    format: "json"
    gatewayUrl: "https://api.mch.weixin.qq.com"
log:
  path: "/www/wwwroot/hcyx/logs"
